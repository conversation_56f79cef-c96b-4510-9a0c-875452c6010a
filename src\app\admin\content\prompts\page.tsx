'use client';

import React, { useState, useEffect } from 'react';
import { 
  Zap, 
  Edit, 
  Save, 
  Copy, 
  TestTube,
  Plus,
  Trash2,
  Eye,
  EyeOff,
  RotateCcw
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: 'content' | 'description' | 'features' | 'pricing' | 'pros_cons';
  template: string;
  variables: string[];
  isActive: boolean;
  lastModified: string;
  usage: number;
}

export default function PromptManagementPage() {
  const [prompts, setPrompts] = useState<PromptTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingPrompt, setEditingPrompt] = useState<PromptTemplate | null>(null);
  const [showTemplate, setShowTemplate] = useState<Record<string, boolean>>({});
  const [testingPrompt, setTestingPrompt] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Load prompt templates
  useEffect(() => {
    const loadPrompts = async () => {
      try {
        setIsLoading(true);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock prompt data
        setPrompts([
          {
            id: '1',
            name: 'Main Content Generation',
            description: 'Primary template for generating tool descriptions',
            category: 'content',
            template: `You are ThePornDude, the irreverent and brutally honest reviewer of AI tools. Write a comprehensive review for {toolName} based on the following information:

URL: {toolUrl}
Scraped Content: {scrapedContent}

Write in your signature style:
- Be direct and no-bullshit
- Use humor and personality
- Focus on practical value
- Include both pros and cons
- Keep it engaging and readable

Structure:
1. Hook opening (2-3 sentences)
2. What it does (main features)
3. Why it matters (practical benefits)
4. The good stuff (pros)
5. The not-so-good (cons)
6. Bottom line verdict

Word count: 800-1200 words
Tone: Irreverent, honest, engaging`,
            variables: ['toolName', 'toolUrl', 'scrapedContent'],
            isActive: true,
            lastModified: '2024-01-15T10:30:00Z',
            usage: 45
          },
          {
            id: '2',
            name: 'Feature Extraction',
            description: 'Extract and format key features from scraped content',
            category: 'features',
            template: `Based on the following content about {toolName}, extract and list the key features in a clear, bulleted format:

Content: {scrapedContent}

Requirements:
- List 5-8 main features
- Be specific and actionable
- Focus on user benefits
- Use clear, jargon-free language
- Format as bullet points

Example format:
• Feature name: Brief description of benefit
• Another feature: What it does for users`,
            variables: ['toolName', 'scrapedContent'],
            isActive: true,
            lastModified: '2024-01-15T09:45:00Z',
            usage: 32
          },
          {
            id: '3',
            name: 'Pros and Cons Analysis',
            description: 'Generate balanced pros and cons lists',
            category: 'pros_cons',
            template: `Analyze {toolName} and provide a balanced assessment of pros and cons based on this information:

{scrapedContent}

Create two lists:

PROS (3-5 items):
- Focus on genuine strengths
- Consider user experience
- Highlight unique features
- Be specific and factual

CONS (2-4 items):
- Identify real limitations
- Consider potential drawbacks
- Be fair but honest
- Avoid nitpicking

Keep each point concise (1-2 sentences max).`,
            variables: ['toolName', 'scrapedContent'],
            isActive: true,
            lastModified: '2024-01-15T09:15:00Z',
            usage: 28
          },
          {
            id: '4',
            name: 'Short Description',
            description: 'Generate concise tool descriptions for listings',
            category: 'description',
            template: `Write a concise, compelling description for {toolName} in 2-3 sentences.

Based on: {scrapedContent}

Requirements:
- Maximum 150 words
- Clear value proposition
- Engaging but professional
- Include main use case
- End with a hook that makes users want to learn more`,
            variables: ['toolName', 'scrapedContent'],
            isActive: false,
            lastModified: '2024-01-14T15:20:00Z',
            usage: 15
          }
        ]);
        
      } catch (err) {
        console.error('Error loading prompts:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadPrompts();
  }, []);

  const toggleTemplateVisibility = (promptId: string) => {
    setShowTemplate(prev => ({
      ...prev,
      [promptId]: !prev[promptId]
    }));
  };

  const handleEditPrompt = (prompt: PromptTemplate) => {
    setEditingPrompt({ ...prompt });
  };

  const handleSavePrompt = async () => {
    if (!editingPrompt) return;
    
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setPrompts(prev => prev.map(p => 
        p.id === editingPrompt.id 
          ? { ...editingPrompt, lastModified: new Date().toISOString() }
          : p
      ));
      
      setEditingPrompt(null);
    } catch (err) {
      console.error('Failed to save prompt:', err);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestPrompt = async (promptId: string) => {
    setTestingPrompt(promptId);
    try {
      // Simulate API test
      await new Promise(resolve => setTimeout(resolve, 3000));
      // In real app, this would test the prompt with sample data
    } catch (err) {
      console.error('Prompt test failed:', err);
    } finally {
      setTestingPrompt(null);
    }
  };

  const handleCopyPrompt = (template: string) => {
    navigator.clipboard.writeText(template);
    // Could add a toast notification here
  };

  const togglePromptStatus = (promptId: string) => {
    setPrompts(prev => prev.map(p => 
      p.id === promptId 
        ? { ...p, isActive: !p.isActive }
        : p
    ));
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'content':
        return 'bg-blue-700 text-blue-200';
      case 'features':
        return 'bg-green-700 text-green-200';
      case 'pros_cons':
        return 'bg-purple-700 text-purple-200';
      case 'description':
        return 'bg-yellow-700 text-yellow-200';
      case 'pricing':
        return 'bg-red-700 text-red-200';
      default:
        return 'bg-gray-700 text-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading prompt templates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Prompt Management</h1>
          <p className="text-gray-400">Manage and optimize AI content generation prompts</p>
        </div>
        
        <button className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
          <Plus className="w-4 h-4" />
          <span>New Prompt</span>
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Prompts</p>
              <p className="text-xl font-bold text-white">{prompts.length}</p>
            </div>
            <Zap className="w-6 h-6 text-blue-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active</p>
              <p className="text-xl font-bold text-white">
                {prompts.filter(p => p.isActive).length}
              </p>
            </div>
            <Zap className="w-6 h-6 text-green-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Usage</p>
              <p className="text-xl font-bold text-white">
                {prompts.reduce((acc, p) => acc + p.usage, 0)}
              </p>
            </div>
            <TestTube className="w-6 h-6 text-purple-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Categories</p>
              <p className="text-xl font-bold text-white">
                {new Set(prompts.map(p => p.category)).size}
              </p>
            </div>
            <Edit className="w-6 h-6 text-yellow-400" />
          </div>
        </div>
      </div>

      {/* Prompts List */}
      <div className="space-y-4">
        {prompts.map((prompt) => (
          <div key={prompt.id} className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="text-lg font-semibold text-white">{prompt.name}</h3>
                  <span className={`text-xs px-2 py-1 rounded ${getCategoryColor(prompt.category)}`}>
                    {prompt.category.replace('_', ' ')}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    prompt.isActive ? 'bg-green-700 text-green-200' : 'bg-gray-700 text-gray-200'
                  }`}>
                    {prompt.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                
                <p className="text-gray-400 mb-3">{prompt.description}</p>
                
                <div className="flex items-center space-x-6 text-sm text-gray-400">
                  <span>Variables: {prompt.variables.join(', ')}</span>
                  <span>Usage: {prompt.usage}</span>
                  <span>Modified: {new Date(prompt.lastModified).toLocaleDateString()}</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => toggleTemplateVisibility(prompt.id)}
                  className="text-gray-400 hover:text-white transition-colors p-2"
                  title={showTemplate[prompt.id] ? "Hide template" : "Show template"}
                >
                  {showTemplate[prompt.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
                
                <button
                  onClick={() => handleCopyPrompt(prompt.template)}
                  className="text-gray-400 hover:text-white transition-colors p-2"
                  title="Copy template"
                >
                  <Copy className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => handleTestPrompt(prompt.id)}
                  disabled={testingPrompt === prompt.id}
                  className="flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 disabled:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                >
                  {testingPrompt === prompt.id ? (
                    <>
                      <LoadingSpinner size="sm" />
                      <span>Testing...</span>
                    </>
                  ) : (
                    <>
                      <TestTube className="w-4 h-4" />
                      <span>Test</span>
                    </>
                  )}
                </button>
                
                <button
                  onClick={() => handleEditPrompt(prompt)}
                  className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                >
                  <Edit className="w-4 h-4" />
                  <span>Edit</span>
                </button>
                
                <button
                  onClick={() => togglePromptStatus(prompt.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm ${
                    prompt.isActive 
                      ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                      : 'bg-green-700 hover:bg-green-600 text-white'
                  }`}
                >
                  {prompt.isActive ? 'Deactivate' : 'Activate'}
                </button>
              </div>
            </div>
            
            {/* Template Display */}
            {showTemplate[prompt.id] && (
              <div className="mt-4 p-4 bg-zinc-700 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-300">Template</h4>
                  <button
                    onClick={() => handleCopyPrompt(prompt.template)}
                    className="text-gray-400 hover:text-white transition-colors"
                    title="Copy template"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                <pre className="text-sm text-gray-300 whitespace-pre-wrap overflow-x-auto">
                  {prompt.template}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Edit Modal */}
      {editingPrompt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-zinc-800 border border-black rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-zinc-700">
              <h2 className="text-xl font-bold text-white">Edit Prompt Template</h2>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)] space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Name</label>
                <input
                  type="text"
                  value={editingPrompt.name}
                  onChange={(e) => setEditingPrompt(prev => prev ? { ...prev, name: e.target.value } : null)}
                  className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <input
                  type="text"
                  value={editingPrompt.description}
                  onChange={(e) => setEditingPrompt(prev => prev ? { ...prev, description: e.target.value } : null)}
                  className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Category</label>
                <select
                  value={editingPrompt.category}
                  onChange={(e) => setEditingPrompt(prev => prev ? { ...prev, category: e.target.value as any } : null)}
                  className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="content">Content</option>
                  <option value="description">Description</option>
                  <option value="features">Features</option>
                  <option value="pricing">Pricing</option>
                  <option value="pros_cons">Pros & Cons</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Template</label>
                <textarea
                  value={editingPrompt.template}
                  onChange={(e) => setEditingPrompt(prev => prev ? { ...prev, template: e.target.value } : null)}
                  rows={15}
                  className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500 font-mono text-sm"
                  placeholder="Enter your prompt template here..."
                />
              </div>
            </div>
            
            <div className="p-6 border-t border-zinc-700 flex justify-end space-x-3">
              <button
                onClick={() => setEditingPrompt(null)}
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Cancel
              </button>
              
              <button
                onClick={handleSavePrompt}
                disabled={isSaving}
                className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                {isSaving ? (
                  <>
                    <LoadingSpinner size="sm" />
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    <span>Save Changes</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
